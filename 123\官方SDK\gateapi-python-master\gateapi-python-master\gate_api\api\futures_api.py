# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from gate_api.api_client import ApiClient
from gate_api.exceptions import (  # noqa: F401
    ApiTypeError,
    ApiValueError
)


class FuturesApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def list_futures_contracts(self, settle, **kwargs):  # noqa: E501
        """List all futures contracts  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_contracts(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.Contract]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_contracts_with_http_info(settle, **kwargs)  # noqa: E501

    def list_futures_contracts_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List all futures contracts  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_contracts_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.Contract], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'limit',
            'offset'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_contracts" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_contracts`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_contracts`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_contracts`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_futures_contracts`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/contracts', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[Contract]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_futures_contract(self, settle, contract, **kwargs):  # noqa: E501
        """Get a single contract  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_futures_contract(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Contract
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_futures_contract_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def get_futures_contract_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Get a single contract  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_futures_contract_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Contract, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_futures_contract" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_futures_contract`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `get_futures_contract`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/contracts/{contract}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Contract',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_order_book(self, settle, contract, **kwargs):  # noqa: E501
        """Futures order book  # noqa: E501

        Bids will be sorted by price from high to low, while asks sorted reversely  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_order_book(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str interval: Order depth. 0 means no aggregation is applied. default to 0
        :param int limit: Maximum number of order depth data in asks or bids
        :param bool with_id: Whether the order book update ID will be returned. This ID increases by 1 on every order book update
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrderBook
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_order_book_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_futures_order_book_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Futures order book  # noqa: E501

        Bids will be sorted by price from high to low, while asks sorted reversely  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_order_book_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str interval: Order depth. 0 means no aggregation is applied. default to 0
        :param int limit: Maximum number of order depth data in asks or bids
        :param bool with_id: Whether the order book update ID will be returned. This ID increases by 1 on every order book update
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrderBook, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'interval',
            'limit',
            'with_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_order_book" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_order_book`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_futures_order_book`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 50:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_order_book`, must be a value less than or equal to `50`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_order_book`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'interval' in local_var_params and local_var_params['interval'] is not None:  # noqa: E501
            query_params.append(('interval', local_var_params['interval']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'with_id' in local_var_params and local_var_params['with_id'] is not None:  # noqa: E501
            query_params.append(('with_id', local_var_params['with_id']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/order_book', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrderBook',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_trades(self, settle, contract, **kwargs):  # noqa: E501
        """Futures trading history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_trades(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify the starting point for this list based on a previously retrieved id  This parameter is deprecated. Use `from` and `to` instead to limit time range
        :param int _from: Specify starting time in Unix seconds. If not specified, `to` and `limit` will be used to limit response items. If items between `from` and `to` are more than `limit`, only `limit` number will be returned. 
        :param int to: Specify end time in Unix seconds, default to current time
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesTrade]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_trades_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_futures_trades_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Futures trading history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_trades_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify the starting point for this list based on a previously retrieved id  This parameter is deprecated. Use `from` and `to` instead to limit time range
        :param int _from: Specify starting time in Unix seconds. If not specified, `to` and `limit` will be used to limit response items. If items between `from` and `to` are more than `limit`, only `limit` number will be returned. 
        :param int to: Specify end time in Unix seconds, default to current time
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesTrade], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'offset',
            'last_id',
            '_from',
            'to'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_trades" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_trades`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_futures_trades`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_trades`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_trades`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_futures_trades`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501
        if 'last_id' in local_var_params and local_var_params['last_id'] is not None:  # noqa: E501
            query_params.append(('last_id', local_var_params['last_id']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/trades', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesTrade]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_candlesticks(self, settle, contract, **kwargs):  # noqa: E501
        """Get futures candlesticks  # noqa: E501

        Return specified contract candlesticks. If prefix `contract` with `mark_`, the contract's mark price candlesticks are returned; if prefix with `index_`, index price candlesticks will be returned.  Maximum of 2000 points are returned in one query. Be sure not to exceed the limit when specifying `from`, `to` and `interval`  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_candlesticks(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int _from: Start time of candlesticks, formatted in Unix timestamp in seconds. Default to`to - 100 * interval` if not specified
        :param int to: End time of candlesticks, formatted in Unix timestamp in seconds. Default to current time
        :param int limit: Maximum recent data points to return. `limit` is conflicted with `from` and `to`. If either `from` or `to` is specified, request will be rejected.
        :param str interval: Interval time between data points. Note that `1w` means natual week(Mon-Sun), while `7d` means every 7d since unix 0.  Note that 30d means 1 natual month, not 30 days
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesCandlestick]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_candlesticks_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_futures_candlesticks_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Get futures candlesticks  # noqa: E501

        Return specified contract candlesticks. If prefix `contract` with `mark_`, the contract's mark price candlesticks are returned; if prefix with `index_`, index price candlesticks will be returned.  Maximum of 2000 points are returned in one query. Be sure not to exceed the limit when specifying `from`, `to` and `interval`  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_candlesticks_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int _from: Start time of candlesticks, formatted in Unix timestamp in seconds. Default to`to - 100 * interval` if not specified
        :param int to: End time of candlesticks, formatted in Unix timestamp in seconds. Default to current time
        :param int limit: Maximum recent data points to return. `limit` is conflicted with `from` and `to`. If either `from` or `to` is specified, request will be rejected.
        :param str interval: Interval time between data points. Note that `1w` means natual week(Mon-Sun), while `7d` means every 7d since unix 0.  Note that 30d means 1 natual month, not 30 days
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesCandlestick], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            '_from',
            'to',
            'limit',
            'interval'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_candlesticks" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_candlesticks`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_futures_candlesticks`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 2000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_candlesticks`, must be a value less than or equal to `2000`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'interval' in local_var_params and local_var_params['interval'] is not None:  # noqa: E501
            query_params.append(('interval', local_var_params['interval']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/candlesticks', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesCandlestick]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_premium_index(self, settle, contract, **kwargs):  # noqa: E501
        """Premium Index K-Line  # noqa: E501

        Maximum of 1000 points can be returned in a query. Be sure not to exceed the limit when specifying from, to and interval  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_premium_index(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int _from: Start time of candlesticks, formatted in Unix timestamp in seconds. Default to`to - 100 * interval` if not specified
        :param int to: End time of candlesticks, formatted in Unix timestamp in seconds. Default to current time
        :param int limit: Maximum recent data points to return. `limit` is conflicted with `from` and `to`. If either `from` or `to` is specified, request will be rejected.
        :param str interval: Interval time between data points
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesPremiumIndex]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_premium_index_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_futures_premium_index_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Premium Index K-Line  # noqa: E501

        Maximum of 1000 points can be returned in a query. Be sure not to exceed the limit when specifying from, to and interval  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_premium_index_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int _from: Start time of candlesticks, formatted in Unix timestamp in seconds. Default to`to - 100 * interval` if not specified
        :param int to: End time of candlesticks, formatted in Unix timestamp in seconds. Default to current time
        :param int limit: Maximum recent data points to return. `limit` is conflicted with `from` and `to`. If either `from` or `to` is specified, request will be rejected.
        :param str interval: Interval time between data points
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesPremiumIndex], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            '_from',
            'to',
            'limit',
            'interval'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_premium_index" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_premium_index`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_futures_premium_index`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 2000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_premium_index`, must be a value less than or equal to `2000`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'interval' in local_var_params and local_var_params['interval'] is not None:  # noqa: E501
            query_params.append(('interval', local_var_params['interval']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/premium_index', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesPremiumIndex]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_tickers(self, settle, **kwargs):  # noqa: E501
        """List futures tickers  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_tickers(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesTicker]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_tickers_with_http_info(settle, **kwargs)  # noqa: E501

    def list_futures_tickers_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List futures tickers  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_tickers_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesTicker], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_tickers" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_tickers`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/tickers', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesTicker]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_funding_rate_history(self, settle, contract, **kwargs):  # noqa: E501
        """Funding rate history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_funding_rate_history(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FundingRateRecord]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_funding_rate_history_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_futures_funding_rate_history_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Funding rate history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_funding_rate_history_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FundingRateRecord], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            '_from',
            'to'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_funding_rate_history" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_funding_rate_history`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_futures_funding_rate_history`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_funding_rate_history`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_funding_rate_history`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/funding_rate', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FundingRateRecord]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_insurance_ledger(self, settle, **kwargs):  # noqa: E501
        """Futures insurance balance history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_insurance_ledger(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.InsuranceRecord]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_insurance_ledger_with_http_info(settle, **kwargs)  # noqa: E501

    def list_futures_insurance_ledger_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Futures insurance balance history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_insurance_ledger_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.InsuranceRecord], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_insurance_ledger" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_insurance_ledger`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_insurance_ledger`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_insurance_ledger`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/insurance', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[InsuranceRecord]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_contract_stats(self, settle, contract, **kwargs):  # noqa: E501
        """Futures stats  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_contract_stats(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int _from: Start timestamp
        :param str interval:
        :param int limit:
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.ContractStat]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_contract_stats_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_contract_stats_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Futures stats  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_contract_stats_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int _from: Start timestamp
        :param str interval:
        :param int limit:
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.ContractStat], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            '_from',
            'interval',
            'limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_contract_stats" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_contract_stats`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_contract_stats`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 100:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_contract_stats`, must be a value less than or equal to `100`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_contract_stats`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'interval' in local_var_params and local_var_params['interval'] is not None:  # noqa: E501
            query_params.append(('interval', local_var_params['interval']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/contract_stats', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[ContractStat]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_index_constituents(self, settle, index, **kwargs):  # noqa: E501
        """Get index constituents  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_index_constituents(settle, index, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str index: Index name (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesIndexConstituents
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_index_constituents_with_http_info(settle, index, **kwargs)  # noqa: E501

    def get_index_constituents_with_http_info(self, settle, index, **kwargs):  # noqa: E501
        """Get index constituents  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_index_constituents_with_http_info(settle, index, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str index: Index name (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesIndexConstituents, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'index'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_index_constituents" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_index_constituents`")  # noqa: E501
        # verify the required parameter 'index' is set
        if self.api_client.client_side_validation and ('index' not in local_var_params or  # noqa: E501
                                                        local_var_params['index'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `index` when calling `get_index_constituents`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'index' in local_var_params:
            path_params['index'] = local_var_params['index']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/index_constituents/{index}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesIndexConstituents',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_liquidated_orders(self, settle, **kwargs):  # noqa: E501
        """Retrieve liquidation history  # noqa: E501

        Interval between `from` and `to` cannot exceeds 3600. Some private fields will not be returned in public endpoints. Refer to field description for detail.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_liquidated_orders(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param int limit: Maximum number of records to be returned in a single list
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesLiqOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_liquidated_orders_with_http_info(settle, **kwargs)  # noqa: E501

    def list_liquidated_orders_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Retrieve liquidation history  # noqa: E501

        Interval between `from` and `to` cannot exceeds 3600. Some private fields will not be returned in public endpoints. Refer to field description for detail.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_liquidated_orders_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param int limit: Maximum number of records to be returned in a single list
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesLiqOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            '_from',
            'to',
            'limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_liquidated_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_liquidated_orders`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_liquidated_orders`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_liquidated_orders`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/liq_orders', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesLiqOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_risk_limit_tiers(self, settle, **kwargs):  # noqa: E501
        """List risk limit tiers  # noqa: E501

        When the 'contract' parameter is not passed, the default is to query the risk limits for the top 100 markets.'Limit' and 'offset' correspond to pagination queries at the market level, not to the length of the returned array. This only takes effect when the 'contract' parameter is empty.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_risk_limit_tiers(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesLimitRiskTiers]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_risk_limit_tiers_with_http_info(settle, **kwargs)  # noqa: E501

    def list_futures_risk_limit_tiers_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List risk limit tiers  # noqa: E501

        When the 'contract' parameter is not passed, the default is to query the risk limits for the top 100 markets.'Limit' and 'offset' correspond to pagination queries at the market level, not to the length of the returned array. This only takes effect when the 'contract' parameter is empty.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_risk_limit_tiers_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesLimitRiskTiers], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'offset'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_risk_limit_tiers" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_risk_limit_tiers`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_risk_limit_tiers`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_risk_limit_tiers`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_futures_risk_limit_tiers`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/risk_limit_tiers', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesLimitRiskTiers]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_accounts(self, settle, **kwargs):  # noqa: E501
        """Query futures account  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_accounts(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesAccount
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_accounts_with_http_info(settle, **kwargs)  # noqa: E501

    def list_futures_accounts_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Query futures account  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_accounts_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesAccount, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_accounts" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_accounts`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/accounts', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesAccount',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_account_book(self, settle, **kwargs):  # noqa: E501
        """Query account book  # noqa: E501

        If the `contract` field is provided, it can only filter records that include this field after 2023-10-30.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_account_book(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param str type: Changing Type：  - dnw: Deposit & Withdraw - pnl: Profit & Loss by reducing position - fee: Trading fee - refr: Referrer rebate - fund: Funding - point_dnw: POINT Deposit & Withdraw - point_fee: POINT Trading fee - point_refr: POINT Referrer rebate - bonus_offset: bouns deduction
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesAccountBook]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_account_book_with_http_info(settle, **kwargs)  # noqa: E501

    def list_futures_account_book_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Query account book  # noqa: E501

        If the `contract` field is provided, it can only filter records that include this field after 2023-10-30.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_account_book_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param str type: Changing Type：  - dnw: Deposit & Withdraw - pnl: Profit & Loss by reducing position - fee: Trading fee - refr: Referrer rebate - fund: Funding - point_dnw: POINT Deposit & Withdraw - point_fee: POINT Trading fee - point_refr: POINT Referrer rebate - bonus_offset: bouns deduction
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesAccountBook], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'offset',
            '_from',
            'to',
            'type'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_account_book" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_account_book`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_account_book`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_account_book`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_futures_account_book`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'type' in local_var_params and local_var_params['type'] is not None:  # noqa: E501
            query_params.append(('type', local_var_params['type']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/account_book', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesAccountBook]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_positions(self, settle, **kwargs):  # noqa: E501
        """List all positions of a user  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_positions(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param bool holding: Return only real positions - true, return all - false.
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.Position]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_positions_with_http_info(settle, **kwargs)  # noqa: E501

    def list_positions_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List all positions of a user  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_positions_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param bool holding: Return only real positions - true, return all - false.
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.Position], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'holding',
            'limit',
            'offset'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_positions" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_positions`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_positions`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_positions`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_positions`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'holding' in local_var_params and local_var_params['holding'] is not None:  # noqa: E501
            query_params.append(('holding', local_var_params['holding']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/positions', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[Position]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_position(self, settle, contract, **kwargs):  # noqa: E501
        """Get single position  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_position(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_position_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def get_position_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Get single position  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_position_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_position" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_position`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `get_position`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/positions/{contract}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_position_margin(self, settle, contract, change, **kwargs):  # noqa: E501
        """Update position margin  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_position_margin(settle, contract, change, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str change: Margin change. Use positive number to increase margin, negative number otherwise. (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_position_margin_with_http_info(settle, contract, change, **kwargs)  # noqa: E501

    def update_position_margin_with_http_info(self, settle, contract, change, **kwargs):  # noqa: E501
        """Update position margin  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_position_margin_with_http_info(settle, contract, change, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str change: Margin change. Use positive number to increase margin, negative number otherwise. (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'change'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_position_margin" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_position_margin`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_position_margin`")  # noqa: E501
        # verify the required parameter 'change' is set
        if self.api_client.client_side_validation and ('change' not in local_var_params or  # noqa: E501
                                                        local_var_params['change'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `change` when calling `update_position_margin`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'change' in local_var_params and local_var_params['change'] is not None:  # noqa: E501
            query_params.append(('change', local_var_params['change']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/positions/{contract}/margin', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_position_leverage(self, settle, contract, leverage, **kwargs):  # noqa: E501
        """Update position leverage  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_position_leverage(settle, contract, leverage, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str leverage: New position leverage (required)
        :param str cross_leverage_limit: Cross margin leverage(valid only when `leverage` is 0)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_position_leverage_with_http_info(settle, contract, leverage, **kwargs)  # noqa: E501

    def update_position_leverage_with_http_info(self, settle, contract, leverage, **kwargs):  # noqa: E501
        """Update position leverage  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_position_leverage_with_http_info(settle, contract, leverage, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str leverage: New position leverage (required)
        :param str cross_leverage_limit: Cross margin leverage(valid only when `leverage` is 0)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'leverage',
            'cross_leverage_limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_position_leverage" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_position_leverage`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_position_leverage`")  # noqa: E501
        # verify the required parameter 'leverage' is set
        if self.api_client.client_side_validation and ('leverage' not in local_var_params or  # noqa: E501
                                                        local_var_params['leverage'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `leverage` when calling `update_position_leverage`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'leverage' in local_var_params and local_var_params['leverage'] is not None:  # noqa: E501
            query_params.append(('leverage', local_var_params['leverage']))  # noqa: E501
        if 'cross_leverage_limit' in local_var_params and local_var_params['cross_leverage_limit'] is not None:  # noqa: E501
            query_params.append(('cross_leverage_limit', local_var_params['cross_leverage_limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/positions/{contract}/leverage', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_position_cross_mode(self, settle, inline_object, **kwargs):  # noqa: E501
        """Switch to the full position-by-store mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_position_cross_mode(settle, inline_object, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param InlineObject inline_object: (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_position_cross_mode_with_http_info(settle, inline_object, **kwargs)  # noqa: E501

    def update_position_cross_mode_with_http_info(self, settle, inline_object, **kwargs):  # noqa: E501
        """Switch to the full position-by-store mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_position_cross_mode_with_http_info(settle, inline_object, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param InlineObject inline_object: (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'inline_object'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_position_cross_mode" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_position_cross_mode`")  # noqa: E501
        # verify the required parameter 'inline_object' is set
        if self.api_client.client_side_validation and ('inline_object' not in local_var_params or  # noqa: E501
                                                        local_var_params['inline_object'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `inline_object` when calling `update_position_cross_mode`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'inline_object' in local_var_params:
            body_params = local_var_params['inline_object']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/positions/cross_mode', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_position_risk_limit(self, settle, contract, risk_limit, **kwargs):  # noqa: E501
        """Update position risk limit  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_position_risk_limit(settle, contract, risk_limit, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str risk_limit: New Risk Limit Value (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_position_risk_limit_with_http_info(settle, contract, risk_limit, **kwargs)  # noqa: E501

    def update_position_risk_limit_with_http_info(self, settle, contract, risk_limit, **kwargs):  # noqa: E501
        """Update position risk limit  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_position_risk_limit_with_http_info(settle, contract, risk_limit, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str risk_limit: New Risk Limit Value (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'risk_limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_position_risk_limit" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_position_risk_limit`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_position_risk_limit`")  # noqa: E501
        # verify the required parameter 'risk_limit' is set
        if self.api_client.client_side_validation and ('risk_limit' not in local_var_params or  # noqa: E501
                                                        local_var_params['risk_limit'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `risk_limit` when calling `update_position_risk_limit`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'risk_limit' in local_var_params and local_var_params['risk_limit'] is not None:  # noqa: E501
            query_params.append(('risk_limit', local_var_params['risk_limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/positions/{contract}/risk_limit', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def set_dual_mode(self, settle, dual_mode, **kwargs):  # noqa: E501
        """Enable or disable dual mode  # noqa: E501

        Before setting dual mode, make sure all positions are closed and no orders are open  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_dual_mode(settle, dual_mode, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param bool dual_mode: Whether to enable dual mode (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesAccount
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.set_dual_mode_with_http_info(settle, dual_mode, **kwargs)  # noqa: E501

    def set_dual_mode_with_http_info(self, settle, dual_mode, **kwargs):  # noqa: E501
        """Enable or disable dual mode  # noqa: E501

        Before setting dual mode, make sure all positions are closed and no orders are open  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_dual_mode_with_http_info(settle, dual_mode, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param bool dual_mode: Whether to enable dual mode (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesAccount, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'dual_mode'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method set_dual_mode" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `set_dual_mode`")  # noqa: E501
        # verify the required parameter 'dual_mode' is set
        if self.api_client.client_side_validation and ('dual_mode' not in local_var_params or  # noqa: E501
                                                        local_var_params['dual_mode'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `dual_mode` when calling `set_dual_mode`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'dual_mode' in local_var_params and local_var_params['dual_mode'] is not None:  # noqa: E501
            query_params.append(('dual_mode', local_var_params['dual_mode']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/dual_mode', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesAccount',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_dual_mode_position(self, settle, contract, **kwargs):  # noqa: E501
        """Retrieve position detail in dual mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_dual_mode_position(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.Position]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_dual_mode_position_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def get_dual_mode_position_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Retrieve position detail in dual mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_dual_mode_position_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.Position], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_dual_mode_position" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_dual_mode_position`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `get_dual_mode_position`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/dual_comp/positions/{contract}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[Position]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_dual_mode_position_margin(self, settle, contract, change, dual_side, **kwargs):  # noqa: E501
        """Update position margin in dual mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_dual_mode_position_margin(settle, contract, change, dual_side, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str change: Margin change. Use positive number to increase margin, negative number otherwise. (required)
        :param str dual_side: Long or short position (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.Position]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_dual_mode_position_margin_with_http_info(settle, contract, change, dual_side, **kwargs)  # noqa: E501

    def update_dual_mode_position_margin_with_http_info(self, settle, contract, change, dual_side, **kwargs):  # noqa: E501
        """Update position margin in dual mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_dual_mode_position_margin_with_http_info(settle, contract, change, dual_side, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str change: Margin change. Use positive number to increase margin, negative number otherwise. (required)
        :param str dual_side: Long or short position (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.Position], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'change',
            'dual_side'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_dual_mode_position_margin" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_dual_mode_position_margin`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_dual_mode_position_margin`")  # noqa: E501
        # verify the required parameter 'change' is set
        if self.api_client.client_side_validation and ('change' not in local_var_params or  # noqa: E501
                                                        local_var_params['change'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `change` when calling `update_dual_mode_position_margin`")  # noqa: E501
        # verify the required parameter 'dual_side' is set
        if self.api_client.client_side_validation and ('dual_side' not in local_var_params or  # noqa: E501
                                                        local_var_params['dual_side'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `dual_side` when calling `update_dual_mode_position_margin`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'change' in local_var_params and local_var_params['change'] is not None:  # noqa: E501
            query_params.append(('change', local_var_params['change']))  # noqa: E501
        if 'dual_side' in local_var_params and local_var_params['dual_side'] is not None:  # noqa: E501
            query_params.append(('dual_side', local_var_params['dual_side']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/dual_comp/positions/{contract}/margin', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[Position]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_dual_mode_position_leverage(self, settle, contract, leverage, **kwargs):  # noqa: E501
        """Update position leverage in dual mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_dual_mode_position_leverage(settle, contract, leverage, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str leverage: New position leverage (required)
        :param str cross_leverage_limit: Cross margin leverage(valid only when `leverage` is 0)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.Position]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_dual_mode_position_leverage_with_http_info(settle, contract, leverage, **kwargs)  # noqa: E501

    def update_dual_mode_position_leverage_with_http_info(self, settle, contract, leverage, **kwargs):  # noqa: E501
        """Update position leverage in dual mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_dual_mode_position_leverage_with_http_info(settle, contract, leverage, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str leverage: New position leverage (required)
        :param str cross_leverage_limit: Cross margin leverage(valid only when `leverage` is 0)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.Position], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'leverage',
            'cross_leverage_limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_dual_mode_position_leverage" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_dual_mode_position_leverage`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_dual_mode_position_leverage`")  # noqa: E501
        # verify the required parameter 'leverage' is set
        if self.api_client.client_side_validation and ('leverage' not in local_var_params or  # noqa: E501
                                                        local_var_params['leverage'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `leverage` when calling `update_dual_mode_position_leverage`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'leverage' in local_var_params and local_var_params['leverage'] is not None:  # noqa: E501
            query_params.append(('leverage', local_var_params['leverage']))  # noqa: E501
        if 'cross_leverage_limit' in local_var_params and local_var_params['cross_leverage_limit'] is not None:  # noqa: E501
            query_params.append(('cross_leverage_limit', local_var_params['cross_leverage_limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/dual_comp/positions/{contract}/leverage', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[Position]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_dual_mode_position_risk_limit(self, settle, contract, risk_limit, **kwargs):  # noqa: E501
        """Update position risk limit in dual mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_dual_mode_position_risk_limit(settle, contract, risk_limit, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str risk_limit: New Risk Limit Value (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.Position]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_dual_mode_position_risk_limit_with_http_info(settle, contract, risk_limit, **kwargs)  # noqa: E501

    def update_dual_mode_position_risk_limit_with_http_info(self, settle, contract, risk_limit, **kwargs):  # noqa: E501
        """Update position risk limit in dual mode  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_dual_mode_position_risk_limit_with_http_info(settle, contract, risk_limit, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str risk_limit: New Risk Limit Value (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.Position], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'risk_limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_dual_mode_position_risk_limit" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_dual_mode_position_risk_limit`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_dual_mode_position_risk_limit`")  # noqa: E501
        # verify the required parameter 'risk_limit' is set
        if self.api_client.client_side_validation and ('risk_limit' not in local_var_params or  # noqa: E501
                                                        local_var_params['risk_limit'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `risk_limit` when calling `update_dual_mode_position_risk_limit`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'risk_limit' in local_var_params and local_var_params['risk_limit'] is not None:  # noqa: E501
            query_params.append(('risk_limit', local_var_params['risk_limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/dual_comp/positions/{contract}/risk_limit', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[Position]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_futures_orders(self, settle, status, **kwargs):  # noqa: E501
        """List futures orders  # noqa: E501

        - Zero-fill order cannot be retrieved for 10 minutes after cancellation - Historical orders, by default, only data within the past 6 months is supported.  If you need to query data for a longer period, please use `GET /futures/{settle}/orders_timerange`.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_orders(settle, status, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str status: Only list the orders with this status (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify list staring point using the `id` of last record in previous list-query results
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_futures_orders_with_http_info(settle, status, **kwargs)  # noqa: E501

    def list_futures_orders_with_http_info(self, settle, status, **kwargs):  # noqa: E501
        """List futures orders  # noqa: E501

        - Zero-fill order cannot be retrieved for 10 minutes after cancellation - Historical orders, by default, only data within the past 6 months is supported.  If you need to query data for a longer period, please use `GET /futures/{settle}/orders_timerange`.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_futures_orders_with_http_info(settle, status, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str status: Only list the orders with this status (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify list staring point using the `id` of last record in previous list-query results
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'status',
            'contract',
            'limit',
            'offset',
            'last_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_futures_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_futures_orders`")  # noqa: E501
        # verify the required parameter 'status' is set
        if self.api_client.client_side_validation and ('status' not in local_var_params or  # noqa: E501
                                                        local_var_params['status'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `status` when calling `list_futures_orders`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_orders`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_futures_orders`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_futures_orders`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'status' in local_var_params and local_var_params['status'] is not None:  # noqa: E501
            query_params.append(('status', local_var_params['status']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501
        if 'last_id' in local_var_params and local_var_params['last_id'] is not None:  # noqa: E501
            query_params.append(('last_id', local_var_params['last_id']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/orders', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_futures_order(self, settle, futures_order, **kwargs):  # noqa: E501
        """Create a futures order  # noqa: E501

        - When placing an order, the number of contracts is specified `size`, not the number of coins. The number of coins corresponding to each contract is returned in the contract details interface `quanto_multiplier` - 0 The order that was completed cannot be obtained after 10 minutes of withdrawal, and the order will be mentioned that the order does not exist - Setting `reduce_only` to `true` can prevent the position from being penetrated when reducing the position - In single-position mode, if you need to close the position, you need to set `size` to 0 and `close` to `true` - In dual warehouse mode,   - Reduce position: reduce_only=true, size is a positive number that indicates short position, negative number that indicates long position  - Add position: reduce_only=false, size is a positive number that indicates adding long positions, and negative numbers indicate adding short positions  - Close position: size=0, set the direction of closing position according to auto_size, and set `reduce_only` to true  at the same time - reduce_only: Make sure to only perform position reduction operations to prevent increased positions - Set `stp_act` to determine the use of a strategy that restricts user transactions. For detailed usage, refer to the body parameter `stp_act`  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_futures_order(settle, futures_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param FuturesOrder futures_order: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.create_futures_order_with_http_info(settle, futures_order, **kwargs)  # noqa: E501

    def create_futures_order_with_http_info(self, settle, futures_order, **kwargs):  # noqa: E501
        """Create a futures order  # noqa: E501

        - When placing an order, the number of contracts is specified `size`, not the number of coins. The number of coins corresponding to each contract is returned in the contract details interface `quanto_multiplier` - 0 The order that was completed cannot be obtained after 10 minutes of withdrawal, and the order will be mentioned that the order does not exist - Setting `reduce_only` to `true` can prevent the position from being penetrated when reducing the position - In single-position mode, if you need to close the position, you need to set `size` to 0 and `close` to `true` - In dual warehouse mode,   - Reduce position: reduce_only=true, size is a positive number that indicates short position, negative number that indicates long position  - Add position: reduce_only=false, size is a positive number that indicates adding long positions, and negative numbers indicate adding short positions  - Close position: size=0, set the direction of closing position according to auto_size, and set `reduce_only` to true  at the same time - reduce_only: Make sure to only perform position reduction operations to prevent increased positions - Set `stp_act` to determine the use of a strategy that restricts user transactions. For detailed usage, refer to the body parameter `stp_act`  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_futures_order_with_http_info(settle, futures_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param FuturesOrder futures_order: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'futures_order',
            'x_gate_exptime'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_futures_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `create_futures_order`")  # noqa: E501
        # verify the required parameter 'futures_order' is set
        if self.api_client.client_side_validation and ('futures_order' not in local_var_params or  # noqa: E501
                                                        local_var_params['futures_order'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `futures_order` when calling `create_futures_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}
        if 'x_gate_exptime' in local_var_params:
            header_params['x-gate-exptime'] = local_var_params['x_gate_exptime']  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if 'futures_order' in local_var_params:
            body_params = local_var_params['futures_order']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/orders', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_futures_orders(self, settle, contract, **kwargs):  # noqa: E501
        """Cancel all `open` orders matched  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_futures_orders(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param str side: Specify all buy orders or all sell orders, if not specify them, both are included. Revoke all buy orders and revoke all sell orders and make ask
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_futures_orders_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def cancel_futures_orders_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Cancel all `open` orders matched  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_futures_orders_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param str side: Specify all buy orders or all sell orders, if not specify them, both are included. Revoke all buy orders and revoke all sell orders and make ask
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'x_gate_exptime',
            'side'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_futures_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_futures_orders`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `cancel_futures_orders`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'side' in local_var_params and local_var_params['side'] is not None:  # noqa: E501
            query_params.append(('side', local_var_params['side']))  # noqa: E501

        header_params = {}
        if 'x_gate_exptime' in local_var_params:
            header_params['x-gate-exptime'] = local_var_params['x_gate_exptime']  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/orders', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_orders_with_time_range(self, settle, **kwargs):  # noqa: E501
        """List Futures Orders By Time Range  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_orders_with_time_range(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_orders_with_time_range_with_http_info(settle, **kwargs)  # noqa: E501

    def get_orders_with_time_range_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List Futures Orders By Time Range  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_orders_with_time_range_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            '_from',
            'to',
            'limit',
            'offset'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_orders_with_time_range" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_orders_with_time_range`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `get_orders_with_time_range`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `get_orders_with_time_range`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `get_orders_with_time_range`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/orders_timerange', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_batch_futures_order(self, settle, futures_order, **kwargs):  # noqa: E501
        """Create a batch of futures orders  # noqa: E501

        - Up to 10 orders per request - If any of the order's parameters are missing or in the wrong format, all of them will not be executed, and a http status 400 error will be returned directly - If the parameters are checked and passed, all are executed. Even if there is a business logic error in the middle (such as insufficient funds), it will not affect other execution orders - The returned result is in array format, and the order corresponds to the orders in the request body - In the returned result, the `succeeded` field of type bool indicates whether the execution was successful or not - If the execution is successful, the normal order content is included; if the execution fails, the `label` field is included to indicate the cause of the error - In the rate limiting, each order is counted individually  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_batch_futures_order(settle, futures_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param list[FuturesOrder] futures_order: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.BatchFuturesOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.create_batch_futures_order_with_http_info(settle, futures_order, **kwargs)  # noqa: E501

    def create_batch_futures_order_with_http_info(self, settle, futures_order, **kwargs):  # noqa: E501
        """Create a batch of futures orders  # noqa: E501

        - Up to 10 orders per request - If any of the order's parameters are missing or in the wrong format, all of them will not be executed, and a http status 400 error will be returned directly - If the parameters are checked and passed, all are executed. Even if there is a business logic error in the middle (such as insufficient funds), it will not affect other execution orders - The returned result is in array format, and the order corresponds to the orders in the request body - In the returned result, the `succeeded` field of type bool indicates whether the execution was successful or not - If the execution is successful, the normal order content is included; if the execution fails, the `label` field is included to indicate the cause of the error - In the rate limiting, each order is counted individually  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_batch_futures_order_with_http_info(settle, futures_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param list[FuturesOrder] futures_order: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.BatchFuturesOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'futures_order',
            'x_gate_exptime'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_batch_futures_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `create_batch_futures_order`")  # noqa: E501
        # verify the required parameter 'futures_order' is set
        if self.api_client.client_side_validation and ('futures_order' not in local_var_params or  # noqa: E501
                                                        local_var_params['futures_order'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `futures_order` when calling `create_batch_futures_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}
        if 'x_gate_exptime' in local_var_params:
            header_params['x-gate-exptime'] = local_var_params['x_gate_exptime']  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if 'futures_order' in local_var_params:
            body_params = local_var_params['futures_order']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/batch_orders', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[BatchFuturesOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_futures_order(self, settle, order_id, **kwargs):  # noqa: E501
        """Get a single order  # noqa: E501

        - Zero-fill order cannot be retrieved for 10 minutes after cancellation - Historical orders, by default, only data within the past 6 months is supported.    # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_futures_order(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Order ID returned, or user custom ID(i.e., `text` field). Operations based on custom ID can only be checked when the order is in orderbook.  When the order is finished, it can be checked within 60 seconds after the end of the order.  After that, only order ID is accepted. (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_futures_order_with_http_info(settle, order_id, **kwargs)  # noqa: E501

    def get_futures_order_with_http_info(self, settle, order_id, **kwargs):  # noqa: E501
        """Get a single order  # noqa: E501

        - Zero-fill order cannot be retrieved for 10 minutes after cancellation - Historical orders, by default, only data within the past 6 months is supported.    # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_futures_order_with_http_info(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Order ID returned, or user custom ID(i.e., `text` field). Operations based on custom ID can only be checked when the order is in orderbook.  When the order is finished, it can be checked within 60 seconds after the end of the order.  After that, only order ID is accepted. (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_futures_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_futures_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `get_futures_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/orders/{order_id}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def amend_futures_order(self, settle, order_id, futures_order_amendment, **kwargs):  # noqa: E501
        """Amend an order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.amend_futures_order(settle, order_id, futures_order_amendment, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Order ID returned, or user custom ID(i.e., `text` field). Operations based on custom ID can only be checked when the order is in orderbook.  When the order is finished, it can be checked within 60 seconds after the end of the order.  After that, only order ID is accepted. (required)
        :param FuturesOrderAmendment futures_order_amendment: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.amend_futures_order_with_http_info(settle, order_id, futures_order_amendment, **kwargs)  # noqa: E501

    def amend_futures_order_with_http_info(self, settle, order_id, futures_order_amendment, **kwargs):  # noqa: E501
        """Amend an order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.amend_futures_order_with_http_info(settle, order_id, futures_order_amendment, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Order ID returned, or user custom ID(i.e., `text` field). Operations based on custom ID can only be checked when the order is in orderbook.  When the order is finished, it can be checked within 60 seconds after the end of the order.  After that, only order ID is accepted. (required)
        :param FuturesOrderAmendment futures_order_amendment: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id',
            'futures_order_amendment',
            'x_gate_exptime'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method amend_futures_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `amend_futures_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `amend_futures_order`")  # noqa: E501
        # verify the required parameter 'futures_order_amendment' is set
        if self.api_client.client_side_validation and ('futures_order_amendment' not in local_var_params or  # noqa: E501
                                                        local_var_params['futures_order_amendment'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `futures_order_amendment` when calling `amend_futures_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}
        if 'x_gate_exptime' in local_var_params:
            header_params['x-gate-exptime'] = local_var_params['x_gate_exptime']  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if 'futures_order_amendment' in local_var_params:
            body_params = local_var_params['futures_order_amendment']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/orders/{order_id}', 'PUT',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_futures_order(self, settle, order_id, **kwargs):  # noqa: E501
        """Cancel a single order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_futures_order(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Order ID returned, or user custom ID(i.e., `text` field). Operations based on custom ID can only be checked when the order is in orderbook.  When the order is finished, it can be checked within 60 seconds after the end of the order.  After that, only order ID is accepted. (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_futures_order_with_http_info(settle, order_id, **kwargs)  # noqa: E501

    def cancel_futures_order_with_http_info(self, settle, order_id, **kwargs):  # noqa: E501
        """Cancel a single order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_futures_order_with_http_info(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Order ID returned, or user custom ID(i.e., `text` field). Operations based on custom ID can only be checked when the order is in orderbook.  When the order is finished, it can be checked within 60 seconds after the end of the order.  After that, only order ID is accepted. (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id',
            'x_gate_exptime'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_futures_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_futures_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `cancel_futures_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}
        if 'x_gate_exptime' in local_var_params:
            header_params['x-gate-exptime'] = local_var_params['x_gate_exptime']  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/orders/{order_id}', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_my_trades(self, settle, **kwargs):  # noqa: E501
        """List personal trading history  # noqa: E501

        By default, only data within the past 6 months is supported.  If you need to query data for a longer period, please use `GET /futures/{settle}/my_trades_timerange`.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_my_trades(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int order: Futures order ID, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify the starting point for this list based on a previously retrieved id  This parameter is deprecated. If you need to iterate through and retrieve more records, we recommend using 'GET /futures/{settle}/my_trades_timerange'.
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.MyFuturesTrade]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_my_trades_with_http_info(settle, **kwargs)  # noqa: E501

    def get_my_trades_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List personal trading history  # noqa: E501

        By default, only data within the past 6 months is supported.  If you need to query data for a longer period, please use `GET /futures/{settle}/my_trades_timerange`.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_my_trades_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int order: Futures order ID, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify the starting point for this list based on a previously retrieved id  This parameter is deprecated. If you need to iterate through and retrieve more records, we recommend using 'GET /futures/{settle}/my_trades_timerange'.
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.MyFuturesTrade], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'order',
            'limit',
            'offset',
            'last_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_my_trades" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_my_trades`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `get_my_trades`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `get_my_trades`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `get_my_trades`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'order' in local_var_params and local_var_params['order'] is not None:  # noqa: E501
            query_params.append(('order', local_var_params['order']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501
        if 'last_id' in local_var_params and local_var_params['last_id'] is not None:  # noqa: E501
            query_params.append(('last_id', local_var_params['last_id']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/my_trades', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[MyFuturesTrade]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_my_trades_with_time_range(self, settle, **kwargs):  # noqa: E501
        """List personal trading history by time range  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_my_trades_with_time_range(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str role: Query role, maker or taker.
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.MyFuturesTradeTimeRange]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_my_trades_with_time_range_with_http_info(settle, **kwargs)  # noqa: E501

    def get_my_trades_with_time_range_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List personal trading history by time range  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_my_trades_with_time_range_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str role: Query role, maker or taker.
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.MyFuturesTradeTimeRange], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            '_from',
            'to',
            'limit',
            'offset',
            'role'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_my_trades_with_time_range" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_my_trades_with_time_range`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `get_my_trades_with_time_range`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `get_my_trades_with_time_range`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `get_my_trades_with_time_range`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501
        if 'role' in local_var_params and local_var_params['role'] is not None:  # noqa: E501
            query_params.append(('role', local_var_params['role']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/my_trades_timerange', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[MyFuturesTradeTimeRange]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_position_close(self, settle, **kwargs):  # noqa: E501
        """List position close history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_position_close(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param str side: Query side.  long or shot
        :param str pnl: Query profit or loss
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.PositionClose]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_position_close_with_http_info(settle, **kwargs)  # noqa: E501

    def list_position_close_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List position close history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_position_close_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param str side: Query side.  long or shot
        :param str pnl: Query profit or loss
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.PositionClose], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'offset',
            '_from',
            'to',
            'side',
            'pnl'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_position_close" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_position_close`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_position_close`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_position_close`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_position_close`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'side' in local_var_params and local_var_params['side'] is not None:  # noqa: E501
            query_params.append(('side', local_var_params['side']))  # noqa: E501
        if 'pnl' in local_var_params and local_var_params['pnl'] is not None:  # noqa: E501
            query_params.append(('pnl', local_var_params['pnl']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/position_close', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[PositionClose]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_liquidates(self, settle, **kwargs):  # noqa: E501
        """List liquidation history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_liquidates(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int at: Specify a liquidation timestamp
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesLiquidate]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_liquidates_with_http_info(settle, **kwargs)  # noqa: E501

    def list_liquidates_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List liquidation history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_liquidates_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int at: Specify a liquidation timestamp
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesLiquidate], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'at'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_liquidates" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_liquidates`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_liquidates`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_liquidates`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'at' in local_var_params and local_var_params['at'] is not None:  # noqa: E501
            query_params.append(('at', local_var_params['at']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/liquidates', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesLiquidate]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_auto_deleverages(self, settle, **kwargs):  # noqa: E501
        """List Auto-Deleveraging History  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_auto_deleverages(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int at: Specify an auto-deleveraging timestamp
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesAutoDeleverage]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_auto_deleverages_with_http_info(settle, **kwargs)  # noqa: E501

    def list_auto_deleverages_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List Auto-Deleveraging History  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_auto_deleverages_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int at: Specify an auto-deleveraging timestamp
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesAutoDeleverage], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'at'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_auto_deleverages" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_auto_deleverages`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_auto_deleverages`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_auto_deleverages`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'at' in local_var_params and local_var_params['at'] is not None:  # noqa: E501
            query_params.append(('at', local_var_params['at']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/auto_deleverages', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesAutoDeleverage]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def countdown_cancel_all_futures(self, settle, countdown_cancel_all_futures_task, **kwargs):  # noqa: E501
        """Countdown cancel orders  # noqa: E501

        When the timeout set by the user is reached, if there is no cancel or set a new countdown, the related pending orders will be automatically cancelled.  This endpoint can be called repeatedly to set a new countdown or cancel the countdown. For example, call this endpoint at 30s intervals, each countdown`timeout` is set to 30s. If this endpoint is not called again within 30 seconds, all pending orders on the specified `market` will be automatically cancelled, if no `market` is specified, all market pending orders will be cancelled. If the `timeout` is set to 0 within 30 seconds, the countdown timer will expire and the cacnel function will be cancelled.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.countdown_cancel_all_futures(settle, countdown_cancel_all_futures_task, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param CountdownCancelAllFuturesTask countdown_cancel_all_futures_task: (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.TriggerTime
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.countdown_cancel_all_futures_with_http_info(settle, countdown_cancel_all_futures_task, **kwargs)  # noqa: E501

    def countdown_cancel_all_futures_with_http_info(self, settle, countdown_cancel_all_futures_task, **kwargs):  # noqa: E501
        """Countdown cancel orders  # noqa: E501

        When the timeout set by the user is reached, if there is no cancel or set a new countdown, the related pending orders will be automatically cancelled.  This endpoint can be called repeatedly to set a new countdown or cancel the countdown. For example, call this endpoint at 30s intervals, each countdown`timeout` is set to 30s. If this endpoint is not called again within 30 seconds, all pending orders on the specified `market` will be automatically cancelled, if no `market` is specified, all market pending orders will be cancelled. If the `timeout` is set to 0 within 30 seconds, the countdown timer will expire and the cacnel function will be cancelled.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.countdown_cancel_all_futures_with_http_info(settle, countdown_cancel_all_futures_task, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param CountdownCancelAllFuturesTask countdown_cancel_all_futures_task: (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.TriggerTime, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'countdown_cancel_all_futures_task'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method countdown_cancel_all_futures" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `countdown_cancel_all_futures`")  # noqa: E501
        # verify the required parameter 'countdown_cancel_all_futures_task' is set
        if self.api_client.client_side_validation and ('countdown_cancel_all_futures_task' not in local_var_params or  # noqa: E501
                                                        local_var_params['countdown_cancel_all_futures_task'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `countdown_cancel_all_futures_task` when calling `countdown_cancel_all_futures`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'countdown_cancel_all_futures_task' in local_var_params:
            body_params = local_var_params['countdown_cancel_all_futures_task']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/countdown_cancel_all', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='TriggerTime',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_futures_fee(self, settle, **kwargs):  # noqa: E501
        """Query user trading fee rates  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_futures_fee(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: dict(str, gate_api.FuturesFee)
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_futures_fee_with_http_info(settle, **kwargs)  # noqa: E501

    def get_futures_fee_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Query user trading fee rates  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_futures_fee_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(dict(str, gate_api.FuturesFee), status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_futures_fee" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_futures_fee`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/fee', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='dict(str, FuturesFee)',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_batch_future_orders(self, settle, request_body, **kwargs):  # noqa: E501
        """Cancel a batch of orders with an ID list  # noqa: E501

        Multiple distinct order ID list can be specified。Each request can cancel a maximum of 20 records.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_batch_future_orders(settle, request_body, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param list[str] request_body: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FutureCancelOrderResult]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_batch_future_orders_with_http_info(settle, request_body, **kwargs)  # noqa: E501

    def cancel_batch_future_orders_with_http_info(self, settle, request_body, **kwargs):  # noqa: E501
        """Cancel a batch of orders with an ID list  # noqa: E501

        Multiple distinct order ID list can be specified。Each request can cancel a maximum of 20 records.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_batch_future_orders_with_http_info(settle, request_body, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param list[str] request_body: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FutureCancelOrderResult], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'request_body',
            'x_gate_exptime'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_batch_future_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_batch_future_orders`")  # noqa: E501
        # verify the required parameter 'request_body' is set
        if self.api_client.client_side_validation and ('request_body' not in local_var_params or  # noqa: E501
                                                        local_var_params['request_body'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `request_body` when calling `cancel_batch_future_orders`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}
        if 'x_gate_exptime' in local_var_params:
            header_params['x-gate-exptime'] = local_var_params['x_gate_exptime']  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if 'request_body' in local_var_params:
            body_params = local_var_params['request_body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/batch_cancel_orders', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FutureCancelOrderResult]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def amend_batch_future_orders(self, settle, batch_amend_order_req, **kwargs):  # noqa: E501
        """Batch modify orders with specified IDs  # noqa: E501

        You can specify multiple different order IDs. You can only modify up to 10 orders in one request.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.amend_batch_future_orders(settle, batch_amend_order_req, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param list[BatchAmendOrderReq] batch_amend_order_req: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.BatchFuturesOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.amend_batch_future_orders_with_http_info(settle, batch_amend_order_req, **kwargs)  # noqa: E501

    def amend_batch_future_orders_with_http_info(self, settle, batch_amend_order_req, **kwargs):  # noqa: E501
        """Batch modify orders with specified IDs  # noqa: E501

        You can specify multiple different order IDs. You can only modify up to 10 orders in one request.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.amend_batch_future_orders_with_http_info(settle, batch_amend_order_req, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param list[BatchAmendOrderReq] batch_amend_order_req: (required)
        :param str x_gate_exptime: Specify the expiration time (milliseconds); if the GATE receives the request time greater than the expiration time, the request will be rejected
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.BatchFuturesOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'batch_amend_order_req',
            'x_gate_exptime'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method amend_batch_future_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `amend_batch_future_orders`")  # noqa: E501
        # verify the required parameter 'batch_amend_order_req' is set
        if self.api_client.client_side_validation and ('batch_amend_order_req' not in local_var_params or  # noqa: E501
                                                        local_var_params['batch_amend_order_req'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `batch_amend_order_req` when calling `amend_batch_future_orders`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}
        if 'x_gate_exptime' in local_var_params:
            header_params['x-gate-exptime'] = local_var_params['x_gate_exptime']  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if 'batch_amend_order_req' in local_var_params:
            body_params = local_var_params['batch_amend_order_req']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/batch_amend_orders', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[BatchFuturesOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_price_triggered_orders(self, settle, status, **kwargs):  # noqa: E501
        """List All Price-triggered Orders  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_price_triggered_orders(settle, status, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str status: Only list the orders with this status (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesPriceTriggeredOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_price_triggered_orders_with_http_info(settle, status, **kwargs)  # noqa: E501

    def list_price_triggered_orders_with_http_info(self, settle, status, **kwargs):  # noqa: E501
        """List All Price-triggered Orders  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_price_triggered_orders_with_http_info(settle, status, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str status: Only list the orders with this status (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesPriceTriggeredOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'status',
            'contract',
            'limit',
            'offset'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_price_triggered_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_price_triggered_orders`")  # noqa: E501
        # verify the required parameter 'status' is set
        if self.api_client.client_side_validation and ('status' not in local_var_params or  # noqa: E501
                                                        local_var_params['status'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `status` when calling `list_price_triggered_orders`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_price_triggered_orders`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_price_triggered_orders`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_price_triggered_orders`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'status' in local_var_params and local_var_params['status'] is not None:  # noqa: E501
            query_params.append(('status', local_var_params['status']))  # noqa: E501
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/price_orders', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesPriceTriggeredOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_price_triggered_order(self, settle, futures_price_triggered_order, **kwargs):  # noqa: E501
        """Create a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_price_triggered_order(settle, futures_price_triggered_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param FuturesPriceTriggeredOrder futures_price_triggered_order: (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.TriggerOrderResponse
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.create_price_triggered_order_with_http_info(settle, futures_price_triggered_order, **kwargs)  # noqa: E501

    def create_price_triggered_order_with_http_info(self, settle, futures_price_triggered_order, **kwargs):  # noqa: E501
        """Create a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_price_triggered_order_with_http_info(settle, futures_price_triggered_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param FuturesPriceTriggeredOrder futures_price_triggered_order: (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.TriggerOrderResponse, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'futures_price_triggered_order'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_price_triggered_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `create_price_triggered_order`")  # noqa: E501
        # verify the required parameter 'futures_price_triggered_order' is set
        if self.api_client.client_side_validation and ('futures_price_triggered_order' not in local_var_params or  # noqa: E501
                                                        local_var_params['futures_price_triggered_order'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `futures_price_triggered_order` when calling `create_price_triggered_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'futures_price_triggered_order' in local_var_params:
            body_params = local_var_params['futures_price_triggered_order']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/price_orders', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='TriggerOrderResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_price_triggered_order_list(self, settle, **kwargs):  # noqa: E501
        """Cancel All Price-triggered Orders  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_price_triggered_order_list(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesPriceTriggeredOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_price_triggered_order_list_with_http_info(settle, **kwargs)  # noqa: E501

    def cancel_price_triggered_order_list_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Cancel All Price-triggered Orders  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_price_triggered_order_list_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract, return related data only if specified
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesPriceTriggeredOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_price_triggered_order_list" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_price_triggered_order_list`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/price_orders', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesPriceTriggeredOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_price_triggered_order(self, settle, order_id, **kwargs):  # noqa: E501
        """Get a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_price_triggered_order(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesPriceTriggeredOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_price_triggered_order_with_http_info(settle, order_id, **kwargs)  # noqa: E501

    def get_price_triggered_order_with_http_info(self, settle, order_id, **kwargs):  # noqa: E501
        """Get a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_price_triggered_order_with_http_info(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesPriceTriggeredOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_price_triggered_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_price_triggered_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `get_price_triggered_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/price_orders/{order_id}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesPriceTriggeredOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_price_triggered_order(self, settle, order_id, **kwargs):  # noqa: E501
        """cancel a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_price_triggered_order(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesPriceTriggeredOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_price_triggered_order_with_http_info(settle, order_id, **kwargs)  # noqa: E501

    def cancel_price_triggered_order_with_http_info(self, settle, order_id, **kwargs):  # noqa: E501
        """cancel a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_price_triggered_order_with_http_info(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesPriceTriggeredOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_price_triggered_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_price_triggered_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `cancel_price_triggered_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/futures/{settle}/price_orders/{order_id}', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesPriceTriggeredOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)
