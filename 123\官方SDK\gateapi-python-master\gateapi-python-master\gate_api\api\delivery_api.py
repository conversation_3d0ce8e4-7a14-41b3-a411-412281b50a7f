# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from gate_api.api_client import ApiClient
from gate_api.exceptions import (  # noqa: F401
    ApiTypeError,
    ApiValueError
)


class DeliveryApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def list_delivery_contracts(self, settle, **kwargs):  # noqa: E501
        """List all futures contracts  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_contracts(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.DeliveryContract]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_contracts_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_contracts_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List all futures contracts  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_contracts_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.DeliveryContract], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_contracts" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_contracts`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/contracts', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[DeliveryContract]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_delivery_contract(self, settle, contract, **kwargs):  # noqa: E501
        """Get a single contract  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_delivery_contract(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.DeliveryContract
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_delivery_contract_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def get_delivery_contract_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Get a single contract  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_delivery_contract_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.DeliveryContract, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_delivery_contract" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_delivery_contract`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `get_delivery_contract`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/contracts/{contract}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeliveryContract',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_order_book(self, settle, contract, **kwargs):  # noqa: E501
        """Futures order book  # noqa: E501

        Bids will be sorted by price from high to low, while asks sorted reversely  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_order_book(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str interval: Order depth. 0 means no aggregation is applied. default to 0
        :param int limit: Maximum number of order depth data in asks or bids
        :param bool with_id: Whether the order book update ID will be returned. This ID increases by 1 on every order book update
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrderBook
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_order_book_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_delivery_order_book_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Futures order book  # noqa: E501

        Bids will be sorted by price from high to low, while asks sorted reversely  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_order_book_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str interval: Order depth. 0 means no aggregation is applied. default to 0
        :param int limit: Maximum number of order depth data in asks or bids
        :param bool with_id: Whether the order book update ID will be returned. This ID increases by 1 on every order book update
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrderBook, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'interval',
            'limit',
            'with_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_order_book" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_order_book`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_delivery_order_book`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 50:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_order_book`, must be a value less than or equal to `50`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_order_book`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'interval' in local_var_params and local_var_params['interval'] is not None:  # noqa: E501
            query_params.append(('interval', local_var_params['interval']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'with_id' in local_var_params and local_var_params['with_id'] is not None:  # noqa: E501
            query_params.append(('with_id', local_var_params['with_id']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/order_book', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrderBook',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_trades(self, settle, contract, **kwargs):  # noqa: E501
        """Futures trading history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_trades(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param str last_id: Specify the starting point for this list based on a previously retrieved id  This parameter is deprecated. Use `from` and `to` instead to limit time range
        :param int _from: Specify starting time in Unix seconds. If not specified, `to` and `limit` will be used to limit response items. If items between `from` and `to` are more than `limit`, only `limit` number will be returned. 
        :param int to: Specify end time in Unix seconds, default to current time
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesTrade]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_trades_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_delivery_trades_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Futures trading history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_trades_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param str last_id: Specify the starting point for this list based on a previously retrieved id  This parameter is deprecated. Use `from` and `to` instead to limit time range
        :param int _from: Specify starting time in Unix seconds. If not specified, `to` and `limit` will be used to limit response items. If items between `from` and `to` are more than `limit`, only `limit` number will be returned. 
        :param int to: Specify end time in Unix seconds, default to current time
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesTrade], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'last_id',
            '_from',
            'to'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_trades" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_trades`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_delivery_trades`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_trades`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_trades`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'last_id' in local_var_params and local_var_params['last_id'] is not None:  # noqa: E501
            query_params.append(('last_id', local_var_params['last_id']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/trades', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesTrade]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_candlesticks(self, settle, contract, **kwargs):  # noqa: E501
        """Get futures candlesticks  # noqa: E501

        Return specified contract candlesticks. If prefix `contract` with `mark_`, the contract's mark price candlesticks are returned; if prefix with `index_`, index price candlesticks will be returned.  Maximum of 2000 points are returned in one query. Be sure not to exceed the limit when specifying `from`, `to` and `interval`  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_candlesticks(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int _from: Start time of candlesticks, formatted in Unix timestamp in seconds. Default to`to - 100 * interval` if not specified
        :param int to: End time of candlesticks, formatted in Unix timestamp in seconds. Default to current time
        :param int limit: Maximum recent data points to return. `limit` is conflicted with `from` and `to`. If either `from` or `to` is specified, request will be rejected.
        :param str interval: Interval time between data points. Note that `1w` means natual week(Mon-Sun), while `7d` means every 7d since unix 0
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.DeliveryCandlestick]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_candlesticks_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def list_delivery_candlesticks_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Get futures candlesticks  # noqa: E501

        Return specified contract candlesticks. If prefix `contract` with `mark_`, the contract's mark price candlesticks are returned; if prefix with `index_`, index price candlesticks will be returned.  Maximum of 2000 points are returned in one query. Be sure not to exceed the limit when specifying `from`, `to` and `interval`  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_candlesticks_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param int _from: Start time of candlesticks, formatted in Unix timestamp in seconds. Default to`to - 100 * interval` if not specified
        :param int to: End time of candlesticks, formatted in Unix timestamp in seconds. Default to current time
        :param int limit: Maximum recent data points to return. `limit` is conflicted with `from` and `to`. If either `from` or `to` is specified, request will be rejected.
        :param str interval: Interval time between data points. Note that `1w` means natual week(Mon-Sun), while `7d` means every 7d since unix 0
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.DeliveryCandlestick], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            '_from',
            'to',
            'limit',
            'interval'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_candlesticks" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_candlesticks`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `list_delivery_candlesticks`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 2000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_candlesticks`, must be a value less than or equal to `2000`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'interval' in local_var_params and local_var_params['interval'] is not None:  # noqa: E501
            query_params.append(('interval', local_var_params['interval']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/candlesticks', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[DeliveryCandlestick]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_tickers(self, settle, **kwargs):  # noqa: E501
        """List futures tickers  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_tickers(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesTicker]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_tickers_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_tickers_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List futures tickers  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_tickers_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesTicker], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_tickers" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_tickers`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/tickers', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesTicker]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_insurance_ledger(self, settle, **kwargs):  # noqa: E501
        """Futures insurance balance history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_insurance_ledger(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.InsuranceRecord]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_insurance_ledger_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_insurance_ledger_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Futures insurance balance history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_insurance_ledger_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.InsuranceRecord], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_insurance_ledger" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_insurance_ledger`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_insurance_ledger`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_insurance_ledger`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/insurance', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[InsuranceRecord]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_accounts(self, settle, **kwargs):  # noqa: E501
        """Query futures account  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_accounts(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesAccount
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_accounts_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_accounts_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Query futures account  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_accounts_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesAccount, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_accounts" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_accounts`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/accounts', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesAccount',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_account_book(self, settle, **kwargs):  # noqa: E501
        """Query account book  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_account_book(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param str type: Changing Type: - dnw: Deposit & Withdraw - pnl: Profit & Loss by reducing position - fee: Trading fee - refr: Referrer rebate - fund: Funding - point_dnw: POINT Deposit & Withdraw - point_fee: POINT Trading fee - point_refr: POINT Referrer rebate
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesAccountBook]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_account_book_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_account_book_with_http_info(self, settle, **kwargs):  # noqa: E501
        """Query account book  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_account_book_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param int limit: Maximum number of records to be returned in a single list
        :param int _from: Start timestamp
        :param int to: End timestamp
        :param str type: Changing Type: - dnw: Deposit & Withdraw - pnl: Profit & Loss by reducing position - fee: Trading fee - refr: Referrer rebate - fund: Funding - point_dnw: POINT Deposit & Withdraw - point_fee: POINT Trading fee - point_refr: POINT Referrer rebate
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesAccountBook], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'limit',
            '_from',
            'to',
            'type'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_account_book" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_account_book`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_account_book`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_account_book`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if '_from' in local_var_params and local_var_params['_from'] is not None:  # noqa: E501
            query_params.append(('from', local_var_params['_from']))  # noqa: E501
        if 'to' in local_var_params and local_var_params['to'] is not None:  # noqa: E501
            query_params.append(('to', local_var_params['to']))  # noqa: E501
        if 'type' in local_var_params and local_var_params['type'] is not None:  # noqa: E501
            query_params.append(('type', local_var_params['type']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/account_book', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesAccountBook]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_positions(self, settle, **kwargs):  # noqa: E501
        """List all positions of a user  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_positions(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.Position]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_positions_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_positions_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List all positions of a user  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_positions_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.Position], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_positions" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_positions`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/positions', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[Position]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_delivery_position(self, settle, contract, **kwargs):  # noqa: E501
        """Get single position  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_delivery_position(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_delivery_position_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def get_delivery_position_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Get single position  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_delivery_position_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_delivery_position" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_delivery_position`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `get_delivery_position`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/positions/{contract}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_delivery_position_margin(self, settle, contract, change, **kwargs):  # noqa: E501
        """Update position margin  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_delivery_position_margin(settle, contract, change, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str change: Margin change. Use positive number to increase margin, negative number otherwise. (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_delivery_position_margin_with_http_info(settle, contract, change, **kwargs)  # noqa: E501

    def update_delivery_position_margin_with_http_info(self, settle, contract, change, **kwargs):  # noqa: E501
        """Update position margin  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_delivery_position_margin_with_http_info(settle, contract, change, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str change: Margin change. Use positive number to increase margin, negative number otherwise. (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'change'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_delivery_position_margin" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_delivery_position_margin`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_delivery_position_margin`")  # noqa: E501
        # verify the required parameter 'change' is set
        if self.api_client.client_side_validation and ('change' not in local_var_params or  # noqa: E501
                                                        local_var_params['change'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `change` when calling `update_delivery_position_margin`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'change' in local_var_params and local_var_params['change'] is not None:  # noqa: E501
            query_params.append(('change', local_var_params['change']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/positions/{contract}/margin', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_delivery_position_leverage(self, settle, contract, leverage, **kwargs):  # noqa: E501
        """Update position leverage  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_delivery_position_leverage(settle, contract, leverage, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str leverage: New position leverage (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_delivery_position_leverage_with_http_info(settle, contract, leverage, **kwargs)  # noqa: E501

    def update_delivery_position_leverage_with_http_info(self, settle, contract, leverage, **kwargs):  # noqa: E501
        """Update position leverage  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_delivery_position_leverage_with_http_info(settle, contract, leverage, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str leverage: New position leverage (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'leverage'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_delivery_position_leverage" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_delivery_position_leverage`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_delivery_position_leverage`")  # noqa: E501
        # verify the required parameter 'leverage' is set
        if self.api_client.client_side_validation and ('leverage' not in local_var_params or  # noqa: E501
                                                        local_var_params['leverage'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `leverage` when calling `update_delivery_position_leverage`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'leverage' in local_var_params and local_var_params['leverage'] is not None:  # noqa: E501
            query_params.append(('leverage', local_var_params['leverage']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/positions/{contract}/leverage', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_delivery_position_risk_limit(self, settle, contract, risk_limit, **kwargs):  # noqa: E501
        """Update position risk limit  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_delivery_position_risk_limit(settle, contract, risk_limit, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str risk_limit: New position risk limit (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.Position
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.update_delivery_position_risk_limit_with_http_info(settle, contract, risk_limit, **kwargs)  # noqa: E501

    def update_delivery_position_risk_limit_with_http_info(self, settle, contract, risk_limit, **kwargs):  # noqa: E501
        """Update position risk limit  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_delivery_position_risk_limit_with_http_info(settle, contract, risk_limit, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str risk_limit: New position risk limit (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.Position, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'risk_limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_delivery_position_risk_limit" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `update_delivery_position_risk_limit`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `update_delivery_position_risk_limit`")  # noqa: E501
        # verify the required parameter 'risk_limit' is set
        if self.api_client.client_side_validation and ('risk_limit' not in local_var_params or  # noqa: E501
                                                        local_var_params['risk_limit'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `risk_limit` when calling `update_delivery_position_risk_limit`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'contract' in local_var_params:
            path_params['contract'] = local_var_params['contract']  # noqa: E501

        query_params = []
        if 'risk_limit' in local_var_params and local_var_params['risk_limit'] is not None:  # noqa: E501
            query_params.append(('risk_limit', local_var_params['risk_limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/positions/{contract}/risk_limit', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Position',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_orders(self, settle, status, **kwargs):  # noqa: E501
        """List futures orders  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_orders(settle, status, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str status: Only list the orders with this status (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify list staring point using the `id` of last record in previous list-query results
        :param int count_total: Whether to return total number matched. Default to 0(no return)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_orders_with_http_info(settle, status, **kwargs)  # noqa: E501

    def list_delivery_orders_with_http_info(self, settle, status, **kwargs):  # noqa: E501
        """List futures orders  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_orders_with_http_info(settle, status, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str status: Only list the orders with this status (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify list staring point using the `id` of last record in previous list-query results
        :param int count_total: Whether to return total number matched. Default to 0(no return)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'status',
            'contract',
            'limit',
            'offset',
            'last_id',
            'count_total'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_orders`")  # noqa: E501
        # verify the required parameter 'status' is set
        if self.api_client.client_side_validation and ('status' not in local_var_params or  # noqa: E501
                                                        local_var_params['status'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `status` when calling `list_delivery_orders`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_orders`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_orders`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_delivery_orders`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'status' in local_var_params and local_var_params['status'] is not None:  # noqa: E501
            query_params.append(('status', local_var_params['status']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501
        if 'last_id' in local_var_params and local_var_params['last_id'] is not None:  # noqa: E501
            query_params.append(('last_id', local_var_params['last_id']))  # noqa: E501
        if 'count_total' in local_var_params and local_var_params['count_total'] is not None:  # noqa: E501
            query_params.append(('count_total', local_var_params['count_total']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/orders', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_delivery_order(self, settle, futures_order, **kwargs):  # noqa: E501
        """Create a futures order  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_delivery_order(settle, futures_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param FuturesOrder futures_order: (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.create_delivery_order_with_http_info(settle, futures_order, **kwargs)  # noqa: E501

    def create_delivery_order_with_http_info(self, settle, futures_order, **kwargs):  # noqa: E501
        """Create a futures order  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_delivery_order_with_http_info(settle, futures_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param FuturesOrder futures_order: (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'futures_order'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_delivery_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `create_delivery_order`")  # noqa: E501
        # verify the required parameter 'futures_order' is set
        if self.api_client.client_side_validation and ('futures_order' not in local_var_params or  # noqa: E501
                                                        local_var_params['futures_order'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `futures_order` when calling `create_delivery_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'futures_order' in local_var_params:
            body_params = local_var_params['futures_order']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/orders', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_delivery_orders(self, settle, contract, **kwargs):  # noqa: E501
        """Cancel all `open` orders matched  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_delivery_orders(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str side: All bids or asks. Both included if not specified
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_delivery_orders_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def cancel_delivery_orders_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Cancel all `open` orders matched  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_delivery_orders_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param str side: All bids or asks. Both included if not specified
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'side'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_delivery_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_delivery_orders`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `cancel_delivery_orders`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'side' in local_var_params and local_var_params['side'] is not None:  # noqa: E501
            query_params.append(('side', local_var_params['side']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/orders', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_delivery_order(self, settle, order_id, **kwargs):  # noqa: E501
        """Get a single order  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_delivery_order(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_delivery_order_with_http_info(settle, order_id, **kwargs)  # noqa: E501

    def get_delivery_order_with_http_info(self, settle, order_id, **kwargs):  # noqa: E501
        """Get a single order  # noqa: E501

        Zero-filled order cannot be retrieved 10 minutes after order cancellation  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_delivery_order_with_http_info(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_delivery_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_delivery_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `get_delivery_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/orders/{order_id}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_delivery_order(self, settle, order_id, **kwargs):  # noqa: E501
        """Cancel a single order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_delivery_order(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_delivery_order_with_http_info(settle, order_id, **kwargs)  # noqa: E501

    def cancel_delivery_order_with_http_info(self, settle, order_id, **kwargs):  # noqa: E501
        """Cancel a single order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_delivery_order_with_http_info(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_delivery_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_delivery_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `cancel_delivery_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/orders/{order_id}', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_my_delivery_trades(self, settle, **kwargs):  # noqa: E501
        """List personal trading history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_my_delivery_trades(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int order: Futures order ID, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify list staring point using the `id` of last record in previous list-query results
        :param int count_total: Whether to return total number matched. Default to 0(no return)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.MyFuturesTrade]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_my_delivery_trades_with_http_info(settle, **kwargs)  # noqa: E501

    def get_my_delivery_trades_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List personal trading history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_my_delivery_trades_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int order: Futures order ID, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param str last_id: Specify list staring point using the `id` of last record in previous list-query results
        :param int count_total: Whether to return total number matched. Default to 0(no return)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.MyFuturesTrade], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'order',
            'limit',
            'offset',
            'last_id',
            'count_total'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_my_delivery_trades" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_my_delivery_trades`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `get_my_delivery_trades`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `get_my_delivery_trades`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `get_my_delivery_trades`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'order' in local_var_params and local_var_params['order'] is not None:  # noqa: E501
            query_params.append(('order', local_var_params['order']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501
        if 'last_id' in local_var_params and local_var_params['last_id'] is not None:  # noqa: E501
            query_params.append(('last_id', local_var_params['last_id']))  # noqa: E501
        if 'count_total' in local_var_params and local_var_params['count_total'] is not None:  # noqa: E501
            query_params.append(('count_total', local_var_params['count_total']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/my_trades', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[MyFuturesTrade]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_position_close(self, settle, **kwargs):  # noqa: E501
        """List position close history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_position_close(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.PositionClose]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_position_close_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_position_close_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List position close history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_position_close_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.PositionClose], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_position_close" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_position_close`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_position_close`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_position_close`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/position_close', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[PositionClose]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_liquidates(self, settle, **kwargs):  # noqa: E501
        """List liquidation history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_liquidates(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param int at: Specify a liquidation timestamp
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesLiquidate]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_liquidates_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_liquidates_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List liquidation history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_liquidates_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param int at: Specify a liquidation timestamp
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesLiquidate], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'at'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_liquidates" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_liquidates`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_liquidates`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_liquidates`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'at' in local_var_params and local_var_params['at'] is not None:  # noqa: E501
            query_params.append(('at', local_var_params['at']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/liquidates', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesLiquidate]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_settlements(self, settle, **kwargs):  # noqa: E501
        """List settlement history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_settlements(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param int at: Specify a settlement timestamp
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.DeliverySettlement]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_settlements_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_settlements_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List settlement history  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_settlements_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param int at: Specify a settlement timestamp
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.DeliverySettlement], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'at'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_settlements" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_settlements`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_settlements`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_settlements`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'at' in local_var_params and local_var_params['at'] is not None:  # noqa: E501
            query_params.append(('at', local_var_params['at']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/settlements', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[DeliverySettlement]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_risk_limit_tiers(self, settle, **kwargs):  # noqa: E501
        """List risk limit tiers  # noqa: E501

        When the 'contract' parameter is not passed, the default is to query the risk limits for the top 100 markets.'Limit' and 'offset' correspond to pagination queries at the market level, not to the length of the returned array. This only takes effect when the 'contract' parameter is empty.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_risk_limit_tiers(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesLimitRiskTiers]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_delivery_risk_limit_tiers_with_http_info(settle, **kwargs)  # noqa: E501

    def list_delivery_risk_limit_tiers_with_http_info(self, settle, **kwargs):  # noqa: E501
        """List risk limit tiers  # noqa: E501

        When the 'contract' parameter is not passed, the default is to query the risk limits for the top 100 markets.'Limit' and 'offset' correspond to pagination queries at the market level, not to the length of the returned array. This only takes effect when the 'contract' parameter is empty.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_risk_limit_tiers_with_http_info(settle, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesLimitRiskTiers], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract',
            'limit',
            'offset'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_risk_limit_tiers" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_delivery_risk_limit_tiers`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_risk_limit_tiers`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_delivery_risk_limit_tiers`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_delivery_risk_limit_tiers`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/risk_limit_tiers', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesLimitRiskTiers]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_price_triggered_delivery_orders(self, settle, status, **kwargs):  # noqa: E501
        """List All Price-triggered Orders  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_price_triggered_delivery_orders(settle, status, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str status: Only list the orders with this status (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesPriceTriggeredOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.list_price_triggered_delivery_orders_with_http_info(settle, status, **kwargs)  # noqa: E501

    def list_price_triggered_delivery_orders_with_http_info(self, settle, status, **kwargs):  # noqa: E501
        """List All Price-triggered Orders  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_price_triggered_delivery_orders_with_http_info(settle, status, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str status: Only list the orders with this status (required)
        :param str contract: Futures contract, return related data only if specified
        :param int limit: Maximum number of records to be returned in a single list
        :param int offset: List offset, starting from 0
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesPriceTriggeredOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'status',
            'contract',
            'limit',
            'offset'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_price_triggered_delivery_orders" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `list_price_triggered_delivery_orders`")  # noqa: E501
        # verify the required parameter 'status' is set
        if self.api_client.client_side_validation and ('status' not in local_var_params or  # noqa: E501
                                                        local_var_params['status'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `status` when calling `list_price_triggered_delivery_orders`")  # noqa: E501

        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] > 1000:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_price_triggered_delivery_orders`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and 'limit' in local_var_params and local_var_params['limit'] < 1:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `limit` when calling `list_price_triggered_delivery_orders`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and 'offset' in local_var_params and local_var_params['offset'] < 0:  # noqa: E501
            raise ApiValueError("Invalid value for parameter `offset` when calling `list_price_triggered_delivery_orders`, must be a value greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'status' in local_var_params and local_var_params['status'] is not None:  # noqa: E501
            query_params.append(('status', local_var_params['status']))  # noqa: E501
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501
        if 'limit' in local_var_params and local_var_params['limit'] is not None:  # noqa: E501
            query_params.append(('limit', local_var_params['limit']))  # noqa: E501
        if 'offset' in local_var_params and local_var_params['offset'] is not None:  # noqa: E501
            query_params.append(('offset', local_var_params['offset']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/price_orders', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesPriceTriggeredOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_price_triggered_delivery_order(self, settle, futures_price_triggered_order, **kwargs):  # noqa: E501
        """Create a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_price_triggered_delivery_order(settle, futures_price_triggered_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param FuturesPriceTriggeredOrder futures_price_triggered_order: (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.TriggerOrderResponse
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.create_price_triggered_delivery_order_with_http_info(settle, futures_price_triggered_order, **kwargs)  # noqa: E501

    def create_price_triggered_delivery_order_with_http_info(self, settle, futures_price_triggered_order, **kwargs):  # noqa: E501
        """Create a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_price_triggered_delivery_order_with_http_info(settle, futures_price_triggered_order, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param FuturesPriceTriggeredOrder futures_price_triggered_order: (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.TriggerOrderResponse, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'futures_price_triggered_order'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_price_triggered_delivery_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `create_price_triggered_delivery_order`")  # noqa: E501
        # verify the required parameter 'futures_price_triggered_order' is set
        if self.api_client.client_side_validation and ('futures_price_triggered_order' not in local_var_params or  # noqa: E501
                                                        local_var_params['futures_price_triggered_order'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `futures_price_triggered_order` when calling `create_price_triggered_delivery_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'futures_price_triggered_order' in local_var_params:
            body_params = local_var_params['futures_price_triggered_order']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/price_orders', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='TriggerOrderResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_price_triggered_delivery_order_list(self, settle, contract, **kwargs):  # noqa: E501
        """Cancel All Price-triggered Orders  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_price_triggered_delivery_order_list(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: list[gate_api.FuturesPriceTriggeredOrder]
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_price_triggered_delivery_order_list_with_http_info(settle, contract, **kwargs)  # noqa: E501

    def cancel_price_triggered_delivery_order_list_with_http_info(self, settle, contract, **kwargs):  # noqa: E501
        """Cancel All Price-triggered Orders  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_price_triggered_delivery_order_list_with_http_info(settle, contract, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str contract: Futures contract (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(list[gate_api.FuturesPriceTriggeredOrder], status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'contract'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_price_triggered_delivery_order_list" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_price_triggered_delivery_order_list`")  # noqa: E501
        # verify the required parameter 'contract' is set
        if self.api_client.client_side_validation and ('contract' not in local_var_params or  # noqa: E501
                                                        local_var_params['contract'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `contract` when calling `cancel_price_triggered_delivery_order_list`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501

        query_params = []
        if 'contract' in local_var_params and local_var_params['contract'] is not None:  # noqa: E501
            query_params.append(('contract', local_var_params['contract']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/price_orders', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='list[FuturesPriceTriggeredOrder]',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_price_triggered_delivery_order(self, settle, order_id, **kwargs):  # noqa: E501
        """Get a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_price_triggered_delivery_order(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesPriceTriggeredOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.get_price_triggered_delivery_order_with_http_info(settle, order_id, **kwargs)  # noqa: E501

    def get_price_triggered_delivery_order_with_http_info(self, settle, order_id, **kwargs):  # noqa: E501
        """Get a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_price_triggered_delivery_order_with_http_info(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesPriceTriggeredOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_price_triggered_delivery_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `get_price_triggered_delivery_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `get_price_triggered_delivery_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/price_orders/{order_id}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesPriceTriggeredOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_price_triggered_delivery_order(self, settle, order_id, **kwargs):  # noqa: E501
        """cancel a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_price_triggered_delivery_order(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: gate_api.FuturesPriceTriggeredOrder
        :return: If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        return self.cancel_price_triggered_delivery_order_with_http_info(settle, order_id, **kwargs)  # noqa: E501

    def cancel_price_triggered_delivery_order_with_http_info(self, settle, order_id, **kwargs):  # noqa: E501
        """cancel a price-triggered order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_price_triggered_delivery_order_with_http_info(settle, order_id, async_req=True)
        >>> result = thread.get()

        :param bool async_req: execute request asynchronously
        :param str settle: Settle currency (required)
        :param str order_id: Retrieve the data of the order with the specified ID (required)
        :param _return_http_data_only: response data without head status code
                                       and headers
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :rtype: tuple(gate_api.FuturesPriceTriggeredOrder, status_code(int), headers(HTTPHeaderDict))
        :return: If the method is called asynchronously,
                 returns the request thread.
        """

        local_var_params = locals()

        all_params = [
            'settle',
            'order_id'
        ]
        all_params.extend(
            [
                'async_req',
                '_return_http_data_only',
                '_preload_content',
                '_request_timeout'
            ]
        )

        for k, v in six.iteritems(local_var_params['kwargs']):
            if k not in all_params:
                raise ApiTypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_price_triggered_delivery_order" % k
                )
            local_var_params[k] = v
        del local_var_params['kwargs']
        # verify the required parameter 'settle' is set
        if self.api_client.client_side_validation and ('settle' not in local_var_params or  # noqa: E501
                                                        local_var_params['settle'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `settle` when calling `cancel_price_triggered_delivery_order`")  # noqa: E501
        # verify the required parameter 'order_id' is set
        if self.api_client.client_side_validation and ('order_id' not in local_var_params or  # noqa: E501
                                                        local_var_params['order_id'] is None):  # noqa: E501
            raise ApiValueError("Missing the required parameter `order_id` when calling `cancel_price_triggered_delivery_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'settle' in local_var_params:
            path_params['settle'] = local_var_params['settle']  # noqa: E501
        if 'order_id' in local_var_params:
            path_params['order_id'] = local_var_params['order_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = ['apiv4']  # noqa: E501

        return self.api_client.call_api(
            '/delivery/{settle}/price_orders/{order_id}', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='FuturesPriceTriggeredOrder',  # noqa: E501
            auth_settings=auth_settings,
            async_req=local_var_params.get('async_req'),
            _return_http_data_only=local_var_params.get('_return_http_data_only'),  # noqa: E501
            _preload_content=local_var_params.get('_preload_content', True),
            _request_timeout=local_var_params.get('_request_timeout'),
            collection_formats=collection_formats)
