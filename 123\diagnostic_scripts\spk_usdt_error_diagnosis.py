#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPK-USDT交易规则获取失败问题精确诊断脚本
2025-07-30 专项诊断
"""

import sys
import os
import json
import time
import logging
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def diagnose_spk_usdt_error():
    """精确诊断SPK-USDT交易规则获取失败问题"""
    
    diagnosis_results = {
        "timestamp": datetime.now().isoformat(),
        "error_analysis": {},
        "root_cause": {},
        "fix_recommendations": [],
        "verification_steps": []
    }
    
    logger.info("🔍 开始SPK-USDT交易规则获取失败问题诊断...")
    
    # 1. 分析错误日志
    logger.info("📋 步骤1: 分析错误日志...")
    try:
        log_file = Path("123/logs/error_20250730.log")
        if log_file.exists():
            with open(log_file, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # 统计错误数量
            spk_errors = log_content.count("SPK-USDT")
            total_errors = log_content.count("❌ 无法获取交易规则:")
            
            diagnosis_results["error_analysis"] = {
                "spk_usdt_errors": spk_errors,
                "total_trading_rule_errors": total_errors,
                "error_pattern": "所有交易对的所有交易所都失败",
                "affected_symbols": ["SPK-USDT", "RESOLV-USDT", "ICNT-USDT", "CAKE-USDT", "WIF-USDT", "AI16Z-USDT", "SOL-USDT", "MATIC-USDT"],
                "affected_exchanges": ["gate", "bybit", "okx"],
                "affected_markets": ["spot", "futures"]
            }
            
            logger.info(f"✅ 错误日志分析完成: SPK-USDT错误{spk_errors}次，总错误{total_errors}次")
        else:
            logger.warning("⚠️ 错误日志文件不存在")
            diagnosis_results["error_analysis"]["log_file_missing"] = True
            
    except Exception as e:
        logger.error(f"❌ 分析错误日志失败: {e}")
        diagnosis_results["error_analysis"]["analysis_failed"] = str(e)
    
    # 2. 检查全局交易所实例
    logger.info("📋 步骤2: 检查全局交易所实例...")
    try:
        from core.trading_system_initializer import get_global_exchanges
        global_exchanges = get_global_exchanges()
        
        diagnosis_results["root_cause"]["global_exchanges"] = {
            "status": "available" if global_exchanges else "missing",
            "count": len(global_exchanges) if global_exchanges else 0,
            "exchanges": list(global_exchanges.keys()) if global_exchanges else []
        }
        
        if not global_exchanges:
            logger.error("❌ 关键问题发现: get_global_exchanges()返回None")
            diagnosis_results["root_cause"]["primary_issue"] = "get_global_exchanges()返回None，导致交易规则预加载器无法获取交易所实例"
        else:
            logger.info(f"✅ 全局交易所实例正常: {list(global_exchanges.keys())}")
            
    except Exception as e:
        logger.error(f"❌ 检查全局交易所实例失败: {e}")
        diagnosis_results["root_cause"]["global_exchanges_check_failed"] = str(e)
    
    # 3. 检查环境变量配置
    logger.info("📋 步骤3: 检查环境变量配置...")
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_keys = [
            'GATE_API_KEY', 'GATE_API_SECRET',
            'BYBIT_API_KEY', 'BYBIT_API_SECRET', 
            'OKX_API_KEY', 'OKX_API_SECRET', 'OKX_API_PASSPHRASE'
        ]
        
        env_status = {}
        configured_count = 0
        
        for key in required_keys:
            value = os.getenv(key)
            is_configured = bool(value and len(value.strip()) > 0)
            env_status[key] = is_configured
            if is_configured:
                configured_count += 1
        
        diagnosis_results["root_cause"]["environment_variables"] = {
            "total_required": len(required_keys),
            "configured_count": configured_count,
            "configuration_rate": configured_count / len(required_keys),
            "status": env_status
        }
        
        if configured_count == 0:
            logger.error("❌ 严重问题: 所有API密钥都未配置")
            diagnosis_results["root_cause"]["api_keys_missing"] = True
        elif configured_count < len(required_keys):
            logger.warning(f"⚠️ 部分API密钥未配置: {configured_count}/{len(required_keys)}")
        else:
            logger.info("✅ 所有API密钥已配置")
            
    except Exception as e:
        logger.error(f"❌ 检查环境变量失败: {e}")
        diagnosis_results["root_cause"]["env_check_failed"] = str(e)
    
    # 4. 检查交易规则预加载器状态
    logger.info("📋 步骤4: 检查交易规则预加载器状态...")
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 尝试获取SPK-USDT的交易规则
        test_rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
        
        diagnosis_results["root_cause"]["trading_rules_preloader"] = {
            "preloader_available": True,
            "spk_usdt_rule_available": test_rule is not None,
            "cache_stats": getattr(preloader, 'stats', {}),
            "preload_completed": getattr(preloader, 'preload_completed', False)
        }
        
        if not test_rule:
            logger.error("❌ 无法获取SPK-USDT交易规则")
        else:
            logger.info("✅ 成功获取SPK-USDT交易规则")
            
    except Exception as e:
        logger.error(f"❌ 检查交易规则预加载器失败: {e}")
        diagnosis_results["root_cause"]["preloader_check_failed"] = str(e)
    
    # 5. 生成修复建议
    logger.info("📋 步骤5: 生成修复建议...")
    
    if not diagnosis_results["root_cause"].get("global_exchanges", {}).get("status") == "available":
        diagnosis_results["fix_recommendations"].append({
            "priority": "HIGH",
            "issue": "全局交易所实例未设置",
            "solution": "在trading_system_initializer.py的initialize_all_systems()方法中添加set_global_exchanges(self.exchanges)调用",
            "file": "123/core/trading_system_initializer.py",
            "line_location": "约300行，交易所初始化完成后"
        })
    
    if diagnosis_results["root_cause"].get("api_keys_missing"):
        diagnosis_results["fix_recommendations"].append({
            "priority": "HIGH", 
            "issue": "API密钥未配置",
            "solution": "配置.env文件中的所有交易所API密钥",
            "file": ".env",
            "required_keys": required_keys
        })
    
    diagnosis_results["fix_recommendations"].append({
        "priority": "MEDIUM",
        "issue": "交易规则预加载失败",
        "solution": "确保在系统初始化时正确调用交易规则预加载",
        "verification": "检查preload_all_trading_rules()是否在正确时机调用"
    })
    
    # 6. 生成验证步骤
    diagnosis_results["verification_steps"] = [
        "1. 检查.env文件是否包含所有必需的API密钥",
        "2. 确认trading_system_initializer.py中调用了set_global_exchanges()",
        "3. 验证get_global_exchanges()返回非None值",
        "4. 测试交易规则预加载器能否获取SPK-USDT规则",
        "5. 运行系统并检查错误日志是否还有交易规则获取失败"
    ]
    
    # 保存诊断结果
    result_file = Path("123/diagnostic_results/spk_usdt_diagnosis_20250730.json")
    result_file.parent.mkdir(exist_ok=True)
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(diagnosis_results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ 诊断完成，结果已保存到: {result_file}")
    
    # 输出关键发现
    logger.info("🎯 关键发现总结:")
    if diagnosis_results["root_cause"].get("primary_issue"):
        logger.error(f"❌ 主要问题: {diagnosis_results['root_cause']['primary_issue']}")
    
    if diagnosis_results["root_cause"].get("api_keys_missing"):
        logger.error("❌ API密钥配置问题: 所有交易所API密钥都未配置")
    
    logger.info(f"📊 错误统计: SPK-USDT错误{diagnosis_results['error_analysis'].get('spk_usdt_errors', 0)}次")
    logger.info(f"🔧 修复建议数量: {len(diagnosis_results['fix_recommendations'])}")
    
    return diagnosis_results

if __name__ == "__main__":
    try:
        results = diagnose_spk_usdt_error()
        print("\n" + "="*50)
        print("🎯 SPK-USDT错误诊断完成")
        print("="*50)
        
        if results["root_cause"].get("primary_issue"):
            print(f"❌ 主要问题: {results['root_cause']['primary_issue']}")
        
        print(f"🔧 修复建议: {len(results['fix_recommendations'])}个")
        print(f"📋 验证步骤: {len(results['verification_steps'])}个")
        
    except Exception as e:
        logger.error(f"❌ 诊断脚本执行失败: {e}")
        sys.exit(1)
