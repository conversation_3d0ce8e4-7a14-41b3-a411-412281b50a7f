2025-07-30 12:17:20 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 12:17:20 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 12:17:20 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 12:17:20 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 12:17:20.741 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 12:17:20.741 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 12:17:20 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 12:17:21 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 12:17:21 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 12:17:25 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 12:17:25.867 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 12:17:30.879 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:17:30.879 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:17:35.893 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 12:17:35.893 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 12:17:35 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 12:17:35.893 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 12:17:40.866 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:17:40.867 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:17:45.880 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 12:17:45.880 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 12:17:45 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 12:17:45.880 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 12:17:50.871 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:17:50.871 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:17:55.871 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 12:17:55.871 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 12:17:55 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 12:17:55.871 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 12:18:00.888 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:18:00.889 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:18:05.884 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 12:18:05.885 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 12:18:05 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 12:18:05 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 12:18:10.863 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 12:18:15.861 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 12:18:20.869 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 12:18:25.895 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 12:18:30.859 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 12:18:35.872 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:18:35.872 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:18:35.897 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 19:13:45 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:13:45 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:13:45 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:13:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:13:45 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 19:13:47 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 19:13:47 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 19:13:51 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 19:14:01 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 19:14:11 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 19:14:21 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 19:14:31 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 19:14:31 [INFO] [OKXExchange] ✅ OKX账户初始化完成
